import SwiftUI

enum ManageContext {
    case quick(items: [QuickResultHistory])
    case plans(slots: [MealSlot])
}

struct ManageSelectionView: View {
    let context: ManageContext
    var onCompleted: (() -> Void)? = nil
    @Environment(\.dismiss) private var dismiss

    @State private var selectedQuick: Set<UUID> = []
    @State private var selectedSlots: Set<UUID> = []
    @State private var resultMessage: String? = nil

    var body: some View {
        NavigationStack {
            content
                .navigationTitle("Manage")
                .toolbar {
                    ToolbarItem(placement: .cancellationAction) {
                        Button("Close") { dismiss() }
                    }
                    ToolbarItem(placement: .confirmationAction) {
                        if selectionCount > 0 {
                            Text("\(selectionCount) selected")
                                .font(.subheadline)
                                .foregroundStyle(.secondary)
                        }
                    }
                }
        }
        .alert(item: Binding(get: {
            resultMessage.map { AlertItem(title: "Result", message: $0) }
        }, set: { _ in })) { item in
            Alert(title: Text(item.title), message: Text(item.message), dismissButton: .default(Text("OK")))
        }
    }

    // MARK: - Content
    @ViewBuilder
    private var content: some View {
        switch context {
        case .quick(let items):
            quickContent(items: items)
        case .plans(let slots):
            plansContent(slots: slots)
        }
    }

    // MARK: - Quick
    private func quickContent(items: [QuickResultHistory]) -> some View {
        VStack(spacing: 0) {
            List {
                ForEach(items) { item in
                    HStack {
                        Button(action: { toggleQuick(item.id) }) {
                            Image(systemName: selectedQuick.contains(item.id) ? "checkmark.circle.fill" : "circle")
                                .foregroundStyle(selectedQuick.contains(item.id) ? Color.accentColor : Color.secondary)
                        }
                        .buttonStyle(.plain)
                        VStack(alignment: .leading, spacing: 4) {
                            Text("\(item.mealType.displayName) · \(item.numberOfDishes) dishes")
                                .font(.headline)
                            Text(dateString(item.generatedAt))
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        Spacer()
                    }
                    .contentShape(Rectangle())
                    .onTapGesture { toggleQuick(item.id) }
                }
            }

            actionBar(actions: [.delete, .addToFavorites, .removeFromFavorites, .share], perform: { action in
                Task { await performQuick(action: action, items: items) }
            })
        }
    }

    private func performQuick(action: ManageAction, items: [QuickResultHistory]) async {
        let selected = items.filter { selectedQuick.contains($0.id) }
        let result = await BatchOperationManager.performQuick(action: action, selected: selected)
        self.resultMessage = result.message
        onCompleted?()
        // Keep selection after operation; parent will refresh
    }

    private func toggleQuick(_ id: UUID) {
        if selectedQuick.contains(id) { selectedQuick.remove(id) } else { selectedQuick.insert(id) }
    }

    // MARK: - Plans
    private func plansContent(slots: [MealSlot]) -> some View {
        let grouped = Dictionary(grouping: slots) { dayKey($0.recipe.scheduledDate) }
        let keys = grouped.keys.sorted()
        return VStack(spacing: 0) {
            List {
                ForEach(keys, id: \.self) { key in
                    Section(header: Text(sectionHeader(for: key))) {
                        ForEach(grouped[key] ?? []) { slot in
                            HStack {
                                Button(action: { toggleSlot(slot.slotId) }) {
                                    Image(systemName: selectedSlots.contains(slot.slotId) ? "checkmark.circle.fill" : "circle")
                                        .foregroundStyle(selectedSlots.contains(slot.slotId) ? Color.accentColor : Color.secondary)
                                }
                                .buttonStyle(.plain)
                                Text("\(slot.mealType.displayName) — \(slot.recipe.title)")
                                    .font(.body)
                                    .lineLimit(1)
                                Spacer()
                            }
                            .contentShape(Rectangle())
                            .onTapGesture { toggleSlot(slot.slotId) }
                        }
                    }
                }
            }

            actionBar(actions: [.delete, .regenerate, .addToFavorites, .removeFromFavorites, .share], perform: { action in
                Task { await performPlans(action: action, slots: slots) }
            })
        }
    }

    private func performPlans(action: ManageAction, slots: [MealSlot]) async {
        let selected = slots.filter { selectedSlots.contains($0.slotId) }
        let result = await BatchOperationManager.performPlans(action: action, selectedSlots: selected)
        self.resultMessage = result.message
        onCompleted?()
    }

    private func toggleSlot(_ id: UUID) {
        if selectedSlots.contains(id) { selectedSlots.remove(id) } else { selectedSlots.insert(id) }
    }

    // MARK: - Common UI
    private func actionBar(actions: [ManageAction], perform: @escaping (ManageAction) -> Void) -> some View {
        VStack(spacing: 8) {
            Divider()
            HStack(spacing: 12) {
                ForEach(actions, id: \.self) { action in
                    Button(role: action.isDestructive ? .destructive : nil) {
                        perform(action)
                    } label: {
                        Text(action.title)
                    }
                    .buttonStyle(.bordered)
                    .disabled(selectionCount == 0 || (action == .viewDetails && selectionCount != 1))
                }
                Spacer()
            }
            .padding(.horizontal)
            if let msg = resultMessage, !msg.isEmpty {
                Text(msg)
                    .font(.footnote)
                    .foregroundStyle(.secondary)
                    .padding(.bottom, 8)
            }
        }
    }

    private var selectionCount: Int {
        switch context {
        case .quick: return selectedQuick.count
        case .plans: return selectedSlots.count
        }
    }

    private func dateString(_ date: Date) -> String {
        let df = DateFormatter()
        df.dateStyle = .medium
        df.timeStyle = .short
        return df.string(from: date)
    }

    private func dayKey(_ date: Date?) -> String {
        guard let date else { return "" }
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        return f.string(from: Calendar.current.startOfDay(for: date))
    }

    private func sectionHeader(for key: String) -> String {
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        if let d = f.date(from: key) {
            let out = DateFormatter()
            out.dateStyle = .medium
            return out.string(from: d)
        }
        return key
    }


}

private struct AlertItem: Identifiable { let id = UUID(); let title: String; let message: String }
