import SwiftUI

struct QuickResultPageView: View {
    let item: QuickResultHistory
    @State private var entry: QuickResultHistory
    @State private var showProtectedAlert: Bool = false

    init(item: QuickResultHistory) {
        self.item = item
        self._entry = State(initialValue: item)
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Header
                RecipeCardHeaderView(
                    date: entry.generatedAt,
                    mealType: entry.mealType,
                    dishCount: entry.numberOfDishes
                )
                .padding(.horizontal)
                .padding(.top, 12)

                // Recipes list (vertical)
                LazyVStack(alignment: .leading, spacing: 12) {
                    ForEach(entry.recipes, id: \.id) { ui in
                        QuickResultCardView(
                            recipe: ui,
                            highlightMeal: entry.mealType,
                            onDelete: { deleteRecipe(ui.id) }
                        )
                        .padding(.horizontal)
                    }
                }
                .padding(.top, 4)
                .accessibilityLabel("Recipes list")

                Spacer(minLength: 12)
            }
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Quick result page")
        .task(id: item.id) {
            // Progressive loading: upgrade preview entry to full details on demand
            if let full = QuickHistoryManager.shared.loadDetail(id: item.id) {
                entry = full
            }
        }
        .alert("Protected Favorite", isPresented: $showProtectedAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text("Cannot delete a favorited recipe from Quick. Remove it from Favorites first.")
        }
    }

    private func deleteRecipe(_ id: String) {
        // Protect favorited recipes from deletion in Quick
        if FavoritesStore.shared.isFavorite(id: id) {
            showProtectedAlert = true
            return
        }
        // Update local state
        let filtered = entry.recipes.filter { $0.id != id }
        let updated = QuickResultHistory(
            id: entry.id,
            mealType: entry.mealType,
            numberOfDishes: filtered.count,
            totalCookTime: entry.totalCookTime,
            cuisines: entry.cuisines,
            additionalRequest: entry.additionalRequest,
            generatedAt: entry.generatedAt,
            recipes: filtered
        )
        entry = updated

        // Persist atomically back to store
        let all = QuickHistoryManager.shared.all()
        var replaced: [QuickResultHistory] = []
        replaced.reserveCapacity(all.count)
        for it in all {
            if it.id == item.id {
                replaced.append(updated)
            } else {
                replaced.append(it)
            }
        }
        QuickHistoryManager.shared.replaceAll(replaced)
    }
}

#Preview("Quick Result Page") {
    let sample = QuickResultHistory(
        mealType: .lunch,
        numberOfDishes: 2,
        totalCookTime: 45,
        cuisines: ["Italian"],
        additionalRequest: nil,
        recipes: [
            .init(id: UUID().uuidString, title: "Pasta Pesto", subtitle: "Fresh basil, pine nuts", estimatedTime: 25),
            .init(id: UUID().uuidString, title: "Caprese Salad", subtitle: "Tomato, mozzarella, basil", estimatedTime: 10)
        ]
    )
    NavigationStack { QuickResultPageView(item: sample) }
}
