import Foundation

@MainActor
public final class QuickHistoryManager {
    static let shared = QuickHistoryManager()

    private let storage = HistoryStorageService.shared

    private init() {
        // Migrate any legacy data from UserDefaults array → index + per-file
        storage.migrateLegacyIfNeeded()
    }

    // MARK: - Public API

    /// Returns all saved quick results, newest first. Uses in-memory cache with disk-backed store.
    func all() -> [QuickResultHistory] {
        storage.loadAllQuick()
    }

    /// Lightweight index (id, titles, counts) for progressive loading UIs.
    func index() -> [QuickHistoryIndexEntry] {
        storage.quickIndex()
    }

    /// Load the full detail for a given history ID if available.
    func loadDetail(id: UUID) -> QuickResultHistory? {
        storage.loadQuick(id: id)
    }

    /// Returns latest quick result if any
    func latest() -> QuickResultHistory? { all().first }

    /// Current count of saved quick results
    var count: Int { all().count }

    /// Maximum allowed quick history items
    var capacity: Int { StorageConfiguration.quickHistoryCapacity }

    /// Whether a new item can be saved (capacity not full)
    var canAcceptNew: Bool { count < capacity }

    enum SaveOutcome { case saved, full }

    /// Save a new quick history item at position 0 (newest first).
    /// Does not auto-evict when at capacity; instead returns `.full` and performs no save.
    @discardableResult
    func save(_ item: QuickResultHistory) -> SaveOutcome {
        guard count < capacity else { return .full }
        storage.saveQuick(item)
        return .saved
    }

    /// Delete an item by ID
    func delete(id: UUID) {
        storage.deleteQuick(id: id)
    }

    /// Replace all items (atomic)
    func replaceAll(_ items: [QuickResultHistory]) {
        storage.replaceAllQuick(items)
    }
}
