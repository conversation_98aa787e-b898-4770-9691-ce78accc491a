# V7 关键Bug修复 - 任务追踪

基于 `v7tasks.json` 文件生成，用于追踪 V7 版本关键Bug修复的进度。

**建议执行顺序**: A → E → C → B → D

---

## ✅ 开发包 A: 扫描流程稳定性 (P0 - 关键)

### **任务 A1: 修复扫描时的相机闪退问题**
- [ ] **a1.1:** 添加 `NSCameraUsageDescription` 到 `Info.plist` 文件。
- [ ] **a1.2:** 在 `StagingViewModel.swift` 中添加相机可用性检查。
- [ ] **a1.3:** 确保 `ImagePicker.swift` 中的检查逻辑一致。
- [ ] **a1.4:** 跨设备测试相机功能，确保在真机和模拟器上表现正常。

### **任务 A2: 统一成分名称格式**
- [ ] **a2.1:** 在 `GeminiAPIService.swift` 中应用名称规范化逻辑。
- [ ] **a2.2:** 验证 `ResultsView.swift` 是否正确显示格式化后的名称。
- [ ] **a2.3:** 测试扫描结果和食材库中的名称格式是否完全一致。

---

## ✅ 开发包 E: 膳食计划生成稳定性 (P0 - 关键)

### **任务 E1: 防止膳食计划生成器崩溃**
- [ ] **e1.1:** 在 `StructuredMealPlanGenerator.swift` 中为 `adapter.generate()` 调用添加 `try-catch` 错误处理。
- [ ] **e1.2:** 在生成前添加对 `RecipeGenerationRequest` 的输入验证。
- [ ] **e1.3:** 优雅地处理空的或食材不足的场景。
- [ ] **e1.4:** 在 `RecipeServiceAdapter.swift` 中添加输入清理和验证。
- [ ] **e1.5:** 全面测试膳食计划生成的各种边缘场景（如空食材库、网络失败等）。

---

## ✅ 开发包 C: 快速历史生命周期完整性 (P0/P1 - 关键管理问题)

### **任务 C1: 修复管理页面的弹窗死循环**
- [ ] **c1.1:** 在 `ManageSelectionView.swift` 中，用 Toast (轻提示) 系统替换导致死循环的 `alert` 弹窗。
- [ ] **c1.2:** 确保提示消息的状态能被正确重置，避免UI冻结。
- [ ] **c1.3:** 验证 `BatchOperationManager.swift` 的异步操作是否正确处理状态转换。
- [ ] **c1.4:** 广泛测试所有管理操作（删除、收藏等），确保稳定性。

### **任务 C2: 修复删除计数器Bug**
- [ ] **c2.1:** 在 `QuickResultPageView.swift` 的 `deleteRecipe()` 方法中添加清理逻辑，移除空的 `QuickResultHistory` 条目。
- [ ] **c2.2:** 添加父视图 (`QuickHistoryView`) 的刷新触发器，确保计数器实时更新。
- [ ] **c2.3:** 增强 `QuickHistoryView.swift` 的自动重载能力，以响应外部数据变化。
- [ ] **c2.4:** 测试删除操作后计数器的准确性。

### **任务 C3: 实现历史计数逻辑**
- [ ] **c3.1:** 在 `CapacityIndicatorView.swift` 中，将容量显示更新为 `[n/10] generations`，明确是按“代”计数。
- [ ] **c3.2:** 为 `QuickHistoryView.swift` 中的卡片添加“x道菜” (x dishes) 的徽章。
- [ ] **c3.3:** 在 `RecipeGeneratorViewModel.swift` 中，更新保存成功的提示信息，明确区分“菜品数”和“生成代数”。
- [ ] **c3.4:** 测试整个UI，确保计数相关的术语和逻辑一致。

---

## ✅ 开发包 B: 生成器用户体验与导航 (P1 - 高优先级)

### **任务 B1: 优化生成器的 Toast (轻提示) 设计**
- [ ] **b1.1:** 在 `RecipeGeneratorView.swift` 中，重新设计 Toast 的位置和大小，使其居中且更易读。
- [ ] **b1.2:** 在食谱预览中添加烹饪时间。
- [ ] **b1.3:** 当食谱超过3个时，实现 `+N more` 的溢出指示器。
- [ ] **b1.4:** 改进“重新生成”和“很好”按钮的样式，突出主次关系。
- [ ] **b1.5:** 为 Toast 添加完整的 VoiceOver 可访问性支持。
- [ ] **b1.6:** 在不同尺寸的设备上测试新的 Toast 设计。

### **任务 B2: 优化生成后的导航**
- [ ] **b2.1:** 在 `RecipeGeneratorViewModel.swift` 中，实现在点击“很好”后，直接导航到“食谱”标签下的“快速历史”子页面。
- [ ] **b2.2:** 为最新的快速历史卡片添加一个持续3秒的“新”徽章，以引导用户。
- [ ] **b2.3:** 确保 `RecipeHistoryTabView.swift` 能正确处理来自外部的状态变化。
- [ ] **b2.4:** 测试导航的一致性，确保无论从哪个页面开始，都能准确跳转。

---

## ✅ 开发包 D: 收藏夹数据完整性 (P1 - 高优先级)

### **任务 D1: 修复收藏夹显示损坏问题**
- [ ] **d1.1:** 在 `FavoritesManager.swift` 的 `resolveUIModel()` 方法中添加数据验证和错误处理。
- [ ] **d1.2:** 在收藏食谱时，存储一个最小化的UI数据快照作为后备。
- [ ] **d1.3:** 在 `FavoritesView.swift` 中，为无法解析的收藏项实现后备渲染（显示“不可用”和移除按钮）。
- [ ] **d1.4:** 添加食谱ID去重逻辑，防止重复收藏。
- [ ] **d1.5:** 实现移除损坏或不可用收藏项的功能。
- [ ] **d1.6:** 添加全面的收藏夹功能测试，特别是针对数据丢失或损坏的场景。
