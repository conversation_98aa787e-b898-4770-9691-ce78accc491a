{"version": "7.0", "date": "2025-01-09", "description": "V7 Critical Bug Fixes Implementation Tasks", "prd_reference": "critical_bug_fixes_prd.md", "implementation_order": ["bundle_a", "bundle_e", "bundle_c", "bundle_b", "bundle_d"], "total_estimated_days": "12-16", "bundles": {"bundle_a": {"name": "Scan Pipeline Stability", "priority": "P0 - Critical", "estimated_days": "2-3", "issues": ["1.1", "2.3"], "dependencies": [], "description": "Fix camera crash and ingredient name formatting inconsistencies", "tasks": {"a1_camera_crash_fix": {"title": "Fix Camera Crash on Scan", "priority": "P0", "estimated_hours": 8, "files": ["Application/Info.plist", "Utilities/ImagePicker.swift", "Features/1_ImageCapture/StagingViewModel.swift"], "subtasks": [{"id": "a1.1", "title": "Add NSCameraUsageDescription to Info.plist", "description": "Add required camera usage description to prevent iOS crash", "file": "Application/Info.plist", "action": "add_entry", "implementation": "Add <key>NSCameraUsageDescription</key><string>This app uses the camera to scan ingredient labels and receipts for automatic pantry management.</string>", "validation": "Verify entry exists and is properly formatted"}, {"id": "a1.2", "title": "Add camera availability check in StagingViewModel", "description": "Add UIImagePickerController.isSourceTypeAvailable(.camera) check before showing camera", "file": "Features/1_ImageCapture/StagingViewModel.swift", "action": "modify_method", "method": "scanReceiptsAndIngredients()", "line_reference": "~177", "implementation": "Guard with availability check before setting showingCamera = true", "validation": "Camera shows on device, Photo Library opens on simulator"}, {"id": "a1.3", "title": "Ensure consistent availability checking in ImagePicker", "description": "Verify all camera access paths have proper availability checks", "file": "Utilities/ImagePicker.swift", "action": "review_and_modify", "method": "takePhoto()", "line_reference": "193", "implementation": "Ensure consistent checking pattern across all camera access methods", "validation": "No crashes on any device configuration"}, {"id": "a1.4", "title": "Test camera functionality across devices", "description": "Validate camera works on physical devices and simulator handles gracefully", "action": "testing", "test_cases": ["iPhone 14 Pro with camera permission granted", "iPhone with camera permission denied", "iOS Simulator (no camera)", "Various iOS versions"], "expected_results": ["Camera opens successfully on physical device", "Settings navigation on permission denial", "Photo Library opens on simulator without message", "No crashes on any configuration"]}]}, "a2_ingredient_formatting": {"title": "Fix Ingredient Name Formatting Consistency", "priority": "P2", "estimated_hours": 6, "files": ["Services/GeminiAPIService.swift", "Features/3_Results/ResultsView.swift", "Utils/NameCanonicalizer.swift"], "subtasks": [{"id": "a2.1", "title": "Apply NameCanonicalizer in GeminiAPIService", "description": "Add name canonicalization to parseIngredientsResponse method", "file": "Services/GeminiAPIService.swift", "action": "modify_method", "method": "parseIngredientsResponse()", "implementation": "Apply NameCanonicalizer.canonicalize() to ingredient names before returning", "validation": "Ingredient names follow title-case formatting rules"}, {"id": "a2.2", "title": "Verify ResultsView displays formatted names", "description": "Ensure scan results display properly formatted ingredient names", "file": "Features/3_Results/ResultsView.swift", "action": "verify_display", "implementation": "Confirm displayed names match NameCanonicalizer output", "validation": "Scan results show 'Whole Milk' not 'whole milk'"}, {"id": "a2.3", "title": "Test formatting consistency", "description": "Validate scan results match pantry name formatting", "action": "testing", "test_cases": ["Scan ingredients with mixed case", "Save scanned ingredients to pantry", "Compare formatting between scan results and pantry items"], "expected_results": ["Scan results show proper title case", "Pantry items maintain same formatting", "Descriptors and acronyms preserved correctly"]}]}}}, "bundle_e": {"name": "Meal Plan Generation Stability", "priority": "P0 - Critical", "estimated_days": "2-3", "issues": ["1.2"], "dependencies": [], "description": "Prevent meal plan generator crashes with comprehensive error handling", "tasks": {"e1_generator_crash_prevention": {"title": "Prevent Meal Plan Generator Crashes", "priority": "P0", "estimated_hours": 10, "files": ["Services/StructuredMealPlanGenerator.swift", "Services/RecipeServiceAdapter.swift"], "subtasks": [{"id": "e1.1", "title": "Add try-catch around adapter.generate() calls", "description": "Wrap RecipeServiceAdapter.generate() calls in comprehensive error handling", "file": "Services/StructuredMealPlanGenerator.swift", "action": "modify_method", "method": "generatePlan()", "line_reference": "~49", "implementation": "Add do-try-catch blocks around adapter.generate() calls with graceful failure handling", "validation": "No crashes during meal plan generation"}, {"id": "e1.2", "title": "Add input validation before generation", "description": "Validate RecipeGenerationRequest inputs before calling adapter", "file": "Services/StructuredMealPlanGenerator.swift", "action": "add_validation", "implementation": "Check pantry count, meal selections, and date ranges before generation attempts", "validation": "Invalid inputs handled gracefully with user feedback"}, {"id": "e1.3", "title": "Handle empty pantry scenario", "description": "Provide graceful handling when pantry is empty or insufficient", "file": "Services/StructuredMealPlanGenerator.swift", "action": "add_error_handling", "implementation": "Return empty plan with user notice when pantry is insufficient for meal generation", "validation": "Empty pantry shows informative message without crash"}, {"id": "e1.4", "title": "Add RecipeServiceAdapter input sanitization", "description": "Ensure adapter handles edge cases and invalid inputs", "file": "Services/RecipeServiceAdapter.swift", "action": "add_validation", "implementation": "Add input sanitization and validation in generate() method", "validation": "Adapter returns errors instead of throwing exceptions"}, {"id": "e1.5", "title": "Test meal plan generation edge cases", "description": "Comprehensive testing of meal plan generation scenarios", "action": "testing", "test_cases": ["Empty pantry meal plan generation", "Invalid date range selection", "Network failure during generation", "All meals selected with minimal ingredients", "Single meal type with insufficient ingredients"], "expected_results": ["User-friendly error messages for all scenarios", "No app crashes under any configuration", "User can retry with adjusted inputs", "App remains stable and responsive"]}]}}}, "bundle_c": {"name": "Quick History Lifecycle Integrity", "priority": "P0/P1 - Critical Management", "estimated_days": "3-4", "issues": ["3.1", "4.1", "4.2"], "dependencies": ["bundle_a", "bundle_e"], "description": "Fix manage page alert loop, delete counter bug, and implement counting logic", "tasks": {"c1_manage_alert_loop_fix": {"title": "Fix Manage Page Alert <PERSON>", "priority": "P0", "estimated_hours": 8, "files": ["Views/ManageSelectionView.swift", "Utils/BatchOperationManager.swift"], "subtasks": [{"id": "c1.1", "title": "Replace alert binding with toast system", "description": "Remove complex Binding(get:set:) alert pattern causing infinite loops", "file": "Views/ManageSelectionView.swift", "action": "refactor_alerts", "line_reference": "34-38", "implementation": "Replace alert() with non-blocking toast presentation system", "validation": "No infinite loops during any manage operation"}, {"id": "c1.2", "title": "Add proper resultMessage state reset", "description": "Ensure resultMessage is properly cleared after displaying feedback", "file": "Views/ManageSelectionView.swift", "action": "fix_state_management", "implementation": "Add proper state cleanup in toast dismissal and operation completion", "validation": "UI remains interactive throughout operations"}, {"id": "c1.3", "title": "Validate BatchOperationManager async handling", "description": "Ensure async operations don't cause state management issues", "file": "Utils/BatchOperationManager.swift", "action": "review_async_operations", "implementation": "Verify proper state transitions in performQuick() and performPlans() methods", "validation": "Async operations complete cleanly with proper feedback"}, {"id": "c1.4", "title": "Test manage operations extensively", "description": "Test all manage page operations for stability", "action": "testing", "test_cases": ["Delete multiple items with mixed favorite status", "Add multiple items to favorites", "Remove items from favorites", "Rapid successive operations", "Operations with network delays"], "expected_results": ["Single toast per operation with accurate counts", "No UI freezing or infinite loops", "Operations complete successfully", "Clear feedback on favorited item protection"]}]}, "c2_delete_counter_fix": {"title": "Fix Delete Counter Bug", "priority": "P1", "estimated_hours": 6, "files": ["Views/QuickResultPageView.swift", "Features/Recipes/QuickHistoryView.swift"], "subtasks": [{"id": "c2.1", "title": "Add empty entry cleanup in deleteRecipe", "description": "Auto-remove QuickResultHistory entries when all recipes deleted", "file": "Views/QuickResultPageView.swift", "action": "modify_method", "method": "deleteRecipe()", "line_reference": "57", "implementation": "Filter out entries with empty recipes array before calling replaceAll()", "validation": "Empty entries automatically removed from manage view"}, {"id": "c2.2", "title": "Add parent view refresh trigger", "description": "Trigger <PERSON><PERSON>w refresh after recipe deletions", "file": "Views/QuickResultPageView.swift", "action": "add_callback", "implementation": "Add notification or callback mechanism to refresh parent view counter", "validation": "Counter updates immediately after last recipe deletion"}, {"id": "c2.3", "title": "Enhance <PERSON>oryView auto-reload", "description": "Add automatic reload capability for external changes", "file": "Features/Recipes/QuickHistoryView.swift", "action": "add_notification_listener", "implementation": "Listen for QuickHistory changes and reload data automatically", "validation": "UI reflects changes without manual refresh"}, {"id": "c2.4", "title": "Test delete counter accuracy", "description": "Validate counter behavior across deletion scenarios", "action": "testing", "test_cases": ["Delete all recipes from a multi-recipe entry", "Delete recipes from multiple entries", "Delete recipes then navigate to manage page", "Verify capacity calculations after deletions"], "expected_results": ["Counter decreases when entry becomes empty", "Manage page shows updated entry count", "Capacity indicators remain accurate", "No ghost entries with zero recipes"]}]}, "c3_counting_logic_implementation": {"title": "Implement History Counting Logic", "priority": "P1", "estimated_hours": 4, "files": ["Features/Recipes/CapacityIndicatorView.swift", "Features/Recipes/QuickHistoryView.swift", "Features/RecipeGenerator/RecipeGeneratorViewModel.swift"], "subtasks": [{"id": "c3.1", "title": "Update capacity header display", "description": "Change capacity display to show generation count clearly", "file": "Features/Recipes/CapacityIndicatorView.swift", "action": "modify_ui_text", "line_reference": "21", "implementation": "Update Text('[\\(count)/\\(capacity)]') to Text('[\\(count)/\\(capacity)] generations')", "validation": "Header clearly indicates generation-based counting"}, {"id": "c3.2", "title": "Add dish count badges to Quick cards", "description": "Show number of dishes per generation on each Quick card", "file": "Features/Recipes/QuickHistoryView.swift", "action": "add_ui_element", "implementation": "Add 'x dishes' badge to each QuickResultHistory card display", "validation": "Each card shows accurate dish count"}, {"id": "c3.3", "title": "Update save toast messaging", "description": "Clarify generation vs dish count in save confirmation", "file": "Features/RecipeGenerator/RecipeGeneratorViewModel.swift", "action": "modify_toast_text", "implementation": "Change save toast to 'Saved X dishes (1 generation) to Quick.'", "validation": "Save message clarifies counting logic"}, {"id": "c3.4", "title": "Test counting consistency across UI", "description": "Validate consistent counting terminology throughout app", "action": "testing", "test_cases": ["Generate multiple recipes and verify all UI elements", "Check capacity indicators, card badges, save messages", "Validate manage page shows generation counts"], "expected_results": ["Consistent 'generation' terminology everywhere", "Clear distinction between dishes and generations", "User understanding improved through clear labeling"]}]}}}, "bundle_b": {"name": "Generator UX and Navigation", "priority": "P1 - High UX Priority", "estimated_days": "2-3", "issues": ["2.1", "2.2"], "dependencies": ["bundle_c"], "description": "Improve generator toast design and post-generation navigation", "tasks": {"b1_toast_design_enhancement": {"title": "Enhance Generator Toast Design", "priority": "P1", "estimated_hours": 8, "files": ["Features/RecipeGenerator/RecipeGeneratorView.swift"], "subtasks": [{"id": "b1.1", "title": "Redesign toast positioning and size", "description": "Change from top-aligned to centered, medium-size card", "file": "Features/RecipeGenerator/RecipeGeneratorView.swift", "action": "modify_ui_component", "component": "GeneratorToastView", "line_reference": "353", "implementation": "Change from .overlay(alignment: .top) to center positioning, increase size to 75% screen width", "validation": "Toast appears centered and appropriately sized on all devices"}, {"id": "b1.2", "title": "Add cooking time to recipe previews", "description": "Show cooking time alongside recipe titles in toast", "file": "Features/RecipeGenerator/RecipeGeneratorView.swift", "action": "enhance_content_display", "component": "ToastPreviewView", "implementation": "Add estimatedTime display for each recipe preview", "validation": "Cooking times visible and properly formatted"}, {"id": "b1.3", "title": "Implement +N more overflow indicator", "description": "Show '+N more' when more than 3 recipes generated", "file": "Features/RecipeGenerator/RecipeGeneratorView.swift", "action": "add_overflow_indicator", "implementation": "Display '+X more' text when recipe count > 3", "validation": "Overflow indicator shows correct count for 4+ recipes"}, {"id": "b1.4", "title": "Improve button styling and hierarchy", "description": "Enhance visual distinction between Regenerate and Good buttons", "file": "Features/RecipeGenerator/RecipeGeneratorView.swift", "action": "modify_button_styles", "implementation": "Regenerate: .bordered style, Good: .borderedProminent with accent color", "validation": "Clear visual hierarchy with Good button being primary action"}, {"id": "b1.5", "title": "Add VoiceOver accessibility support", "description": "Ensure toast is fully accessible with VoiceOver", "file": "Features/RecipeGenerator/RecipeGeneratorView.swift", "action": "add_accessibility", "implementation": "Add accessibility labels for recipe titles, cooking times, and actions", "validation": "VoiceOver correctly announces all toast content"}, {"id": "b1.6", "title": "Test toast design across devices", "description": "Validate toast appearance and usability on different screen sizes", "action": "testing", "test_cases": ["iPhone 14 Pro (standard screen)", "iPhone 15 Pro Max (large screen)", "iOS 17+ compatible devices only", "Landscape and portrait orientations"], "expected_results": ["Readable on all device sizes", "Proper centering and sizing", "Touch targets appropriately sized", "Content not cut off or overlapping"]}]}, "b2_navigation_enhancement": {"title": "Enhance Post-Generation Navigation", "priority": "P1", "estimated_hours": 4, "files": ["Features/RecipeGenerator/RecipeGeneratorViewModel.swift", "Features/Recipes/RecipeHistoryTabView.swift"], "subtasks": [{"id": "b2.1", "title": "Add sub-tab targeting in acceptQuickPreview", "description": "Set Recipes tab to Quick before navigation", "file": "Features/RecipeGenerator/RecipeGeneratorViewModel.swift", "action": "modify_method", "method": "acceptQuickPreview()", "line_reference": "263", "implementation": "Set @AppStorage('recipes.selectedTab') = 'quick' before switchToTab(3)", "validation": "User lands on Recipes > Quick sub-tab after Good tap"}, {"id": "b2.2", "title": "Add New badge to latest Quick card", "description": "Show 3-second 'New' badge on newest Quick entry for orientation", "file": "Features/Recipes/RecipeHistoryTabView.swift", "action": "add_visual_indicator", "implementation": "Add temporary 'New' badge to most recent entry, auto-remove after 3 seconds", "validation": "New badge appears and disappears correctly"}, {"id": "b2.3", "title": "Ensure sub-tab selection handling", "description": "Verify RecipeHistoryTabView properly handles external sub-tab changes", "file": "Features/Recipes/RecipeHistoryTabView.swift", "action": "verify_state_handling", "implementation": "Confirm @AppStorage changes trigger proper sub-tab selection", "validation": "Sub-tab responds to external selection changes"}, {"id": "b2.4", "title": "Test navigation consistency", "description": "Validate navigation behavior across different starting states", "action": "testing", "test_cases": ["Navigate from different tabs (Pantry, Generator, Profile)", "Navigate with different prior Recipes sub-tab states", "Rapid navigation (multiple Quick generations)", "Navigation with app backgrounding/foregrounding"], "expected_results": ["Always lands on Recipes > Quick regardless of prior state", "New badge appears consistently", "Smooth transitions without UI glitches", "Behavior consistent across all scenarios"]}]}}}, "bundle_d": {"name": "Favorites Data Integrity", "priority": "P1 - High Data Priority", "estimated_days": "2-3", "issues": ["4.3"], "dependencies": [], "description": "Fix favorites display corruption and implement graceful data handling", "tasks": {"d1_favorites_corruption_fix": {"title": "Fix Favorites Display Corruption", "priority": "P1", "estimated_hours": 10, "files": ["Services/FavoritesManager.swift", "Features/Recipes/FavoritesView.swift"], "subtasks": [{"id": "d1.1", "title": "Add data validation in resolveUIModel", "description": "Add comprehensive error handling and validation in FavoritesManager", "file": "Services/FavoritesManager.swift", "action": "enhance_error_handling", "method": "resolveUIModel()", "line_reference": "80", "implementation": "Add validation for recipe data existence and integrity before returning UIModel", "validation": "Method gracefully handles missing or corrupted recipe data"}, {"id": "d1.2", "title": "Implement UI snapshot storage during favoriting", "description": "Store minimal UI data snapshot when recipe is favorited", "file": "Services/FavoritesManager.swift", "action": "add_snapshot_storage", "implementation": "Store (id, title, subtitle, time) snapshot in FavoriteItem creation", "validation": "Snapshot data available for fallback rendering"}, {"id": "d1.3", "title": "Add fallback rendering in FavoritesView", "description": "Implement graceful fallback U<PERSON> for unresolved favorites", "file": "Features/Recipes/FavoritesView.swift", "action": "add_fallback_ui", "method": "row()", "line_reference": "35", "implementation": "Show snapshot data with 'Unavailable' label and Remove button when resolveUIModel fails", "validation": "Corrupted favorites display meaningful information with recovery option"}, {"id": "d1.4", "title": "Add recipe ID deduplication", "description": "Prevent duplicate favorites by canonical recipe ID", "file": "Services/FavoritesManager.swift", "action": "add_deduplication", "implementation": "Check for existing recipe ID before adding to favorites", "validation": "No duplicate recipes in favorites list"}, {"id": "d1.5", "title": "Implement corrupted entry removal", "description": "Allow users to remove corrupted/unavailable favorites", "file": "Features/Recipes/FavoritesView.swift", "action": "add_removal_action", "implementation": "Add Remove button for unavailable entries that cleans up FavoritesStore", "validation": "Users can clean up corrupted favorites easily"}, {"id": "d1.6", "title": "Add comprehensive favorites testing", "description": "Test favorites with missing/corrupted data scenarios", "action": "testing", "test_cases": ["Favorite recipe then delete from Quick/Plans", "Favorite recipe with corrupted ID reference", "Multiple favorites with mixed availability", "Favorites list with all unavailable entries", "Remove corrupted favorites and verify cleanup"], "expected_results": ["No 'numbers and letters' display anywhere", "All rows either navigate or show remove option", "Clear visual distinction for unavailable items", "Removal actions work correctly", "No crashes with any data corruption scenario"]}]}}}}, "cross_bundle_tasks": {"integration_testing": {"title": "Cross-Bundle Integration Testing", "priority": "P0", "estimated_hours": 8, "dependencies": ["bundle_a", "bundle_e", "bundle_c", "bundle_b", "bundle_d"], "subtasks": [{"id": "integration.1", "title": "Full workflow testing", "description": "Test complete user workflows across all fixed components", "action": "end_to_end_testing", "test_scenarios": ["Complete scan → results → pantry workflow", "Full meal plan generation cycle", "Quick recipe generation → save → management → favorites", "Cross-tab navigation and state consistency"]}, {"id": "integration.2", "title": "Regression testing", "description": "Ensure fixes don't break existing functionality", "action": "regression_testing", "focus_areas": ["Existing pantry functionality", "Profile and settings features", "Navigation between all tabs", "Performance impact validation"]}, {"id": "integration.3", "title": "Device matrix validation", "description": "Test on comprehensive device matrix", "action": "device_testing", "devices": ["iPhone 14 Pro (iOS 17+)", "iPhone 15 Pro Max (iOS 17+)", "iOS Simulator (iOS 17+)"]}]}, "performance_validation": {"title": "Performance Impact Validation", "priority": "P1", "estimated_hours": 4, "subtasks": [{"id": "perf.1", "title": "Memory usage validation", "description": "Ensure fixes don't introduce memory leaks or excessive usage", "focus_areas": ["Toast memory management", "History cleanup efficiency", "Favorites data handling"]}, {"id": "perf.2", "title": "UI responsiveness testing", "description": "Validate UI remains responsive after fixes", "focus_areas": ["Quick history operations", "Manage page interactions", "Toast animations"]}]}, "release_preparation": {"title": "Release Preparation and Validation", "priority": "P0", "estimated_hours": 6, "subtasks": [{"id": "release.1", "title": "Success metrics validation", "description": "Verify all acceptance criteria are met", "validation_points": ["0% crash rate on camera access and meal plan generation", "0% forced app restarts from manage page", "100% navigation success rate to Recipes > Quick", "100% favorites displaying valid information"]}, {"id": "release.2", "title": "Telemetry implementation", "description": "Implement anonymous telemetry collection for monitoring", "data_points": ["Camera access success/failure rates", "Meal plan generation completion rates", "Generator choice rates (Regenerate vs Good)", "Manage operation outcomes", "Favorites resolution failure rates"]}, {"id": "release.3", "title": "Release notes preparation", "description": "Document changes and improvements for release", "content": ["Fixed camera crash when scanning ingredients", "Resolved meal plan generator stability issues", "Improved Quick recipe management and navigation", "Enhanced generator toast design and usability", "Fixed favorites display and data integrity issues"]}]}}, "success_criteria": {"crash_elimination": {"camera_access": "0% crash rate on all devices and configurations", "meal_plan_generation": "0% crash rate across all input scenarios", "manage_page": "0% forced app restarts from infinite alert loops"}, "user_experience": {"navigation_success": "100% success rate for Generator → Recipes > Quick navigation", "toast_usability": "95%+ user satisfaction with improved toast design", "data_consistency": "100% accurate counters and displays across all features"}, "data_integrity": {"favorites_display": "100% favorites show valid information with graceful fallbacks", "history_accuracy": "0% ghost entries, accurate counter displays", "formatting_consistency": "100% consistent ingredient name formatting across flows"}}}